// <PERSON><PERSON>uza – Discord Moderation Worker (v7)
// ============================================================================
//  Modes:
//  1. Passive scanner: Scans messages and flags content using OpenAI moderation
//  2. Slash commands: /activate, /status to manage the bot configuration
//
//  Flow per HTTP POST from Discord:
//   1. Verify Ed25519 signature (same as interactions).
//   2. Check if it's an INTERACTION (command) or MESSAGE_CREATE event
//   3. For INTERACTION: Handle /activate or /status commands
//   4. For MESSAGE_CREATE:
//      a. Skip bot/self messages
//      b. Check if guild is activated in AGENT_CONFIG_KV
//      c. Run OpenAI moderation using Commuza's global OpenAI API key
//      d. If `flagged == true` → post an alert back to the same channel
//
//  Environment bindings expected:
//   * DISCORD_PUBLIC_KEY  – for signature verification
//   * DISCORD_BOT_TOKEN   – "Bot <token>" with MESSAGE_CONTENT intent enabled
//   * AGENT_CONFIG_KV     – KV namespace storing `{ activated: true, commuzaApiKey?: string }` per guild
//   * OPENAI_API_KEY      – secret used for moderation endpoint (global)
//   * COMMUZA_API_BASE    – base URL for Commuza API for application key validation
//   * COMMUZA_DB_URL      – Supabase direct URL
//   * COMMUZA_DB_KEY      – Supabase service role key
// ============================================================================

import { verifyKey } from "discord-interactions";

interface Env {
  DISCORD_PUBLIC_KEY: string;
  DISCORD_BOT_TOKEN: string;
  OPENAI_API_KEY: string;
  COMMUZA_API_BASE: string;
  AGENT_CONFIG_KV: KVNamespace;
  COMMUZA_DB_URL: string;  // Supabase direct URL
  COMMUZA_DB_KEY: string;  // Supabase service role key
}

interface DiscordInteraction {
  type: number;
  guild_id: string;
  data?: {
    name?: string;
    options?: Array<{ value: string }>;
  };
}

interface DiscordMessage {
  id: string;
  guild_id: string;
  channel_id: string;
  content?: string;
  author: {
    id: string;
    bot?: boolean;
  };
}

interface GatewayEvent {
  t?: string;
  d?: DiscordMessage;
}

interface GuildConfig {
  activated: boolean;
  commuzaApiKey?: string;
  agent_id?: string;
  organization_id?: string;
}

interface ModerationResult {
  flagged: boolean;
  categories: Record<string, boolean>;
}

interface CachedConfig {
  config: GuildConfig | null;
  expiresAt: number;
}

interface ApiKeyValidationResult {
  valid: boolean;
  message?: string;
  agent_id?: string; // Added to store the DB ID for the discord agent
  organization_id?: string; // Added to store org ID for analytics
  key_expires_at?: string; // Added to store when the API key expires
  days_until_expiry?: number; // Added to store days until expiry for easier display
  revoked?: boolean; // Added to track if the key has been revoked
}

// API key validation cache with TTL
interface CachedValidationResult extends ApiKeyValidationResult {
  expiresAt: number; // Cache expiration time
}
const validationCache = new Map<string, CachedValidationResult>();
const VALIDATION_CACHE_TTL = 5 * 60_000; // 5 minutes

// Guild config cache
const configCache = new Map<string, CachedConfig>();
const CONFIG_CACHE_TTL = 30_000; // 30 seconds

const DISCORD_WSS = "wss://gateway.discord.gg"

// Exported for testing purposes
function _resetCaches() {
  validationCache.clear();
  configCache.clear();
  console.log("[TEST_DEBUG] _resetCaches: Caches cleared.");
}

// Clear validation cache for a specific API key
function clearValidationCache(commuzaApiKey: string, guildId?: string) {
  const cacheKey = `${commuzaApiKey}:${guildId || ""}`;
  validationCache.delete(cacheKey);
  console.log(`[TEST_DEBUG] Cleared validation cache for key prefix: ${commuzaApiKey.substring(0, Math.min(5, commuzaApiKey.length))}`);
}

export default {
  async fetch(
    request: Request,
    env: Env,
    ctx: ExecutionContext
  ): Promise<Response> {
    if (request.method !== "POST") {
      return new Response("Method not allowed", { status: 405 });
    }

    const body = await request.text();

    const payload = JSON.parse(body);

    // PING event must be handled first
    if (payload.type === 0) {
      return new Response(null, {
        headers: { "Content-Type": "application/json" },
        status: 204,
      });
    }

    const sig = request.headers.get("X-Signature-Ed25519");
    const ts = request.headers.get("X-Signature-Timestamp");
    if (!sig || !ts) {
      return new Response("Missing signature headers", { status: 401 });
    }
    if (!(await verifyKey(body, sig, ts, env.DISCORD_PUBLIC_KEY))) {
      return new Response("Bad request signature", { status: 401 });
    }

    if (payload.type === 1) {
      return jsonResponse({ type: 1 });
    }
    if (payload.type === 2) {
      return handleInteraction(payload, env);
    }

    const { t: eventName, d: data } = payload.t
      ? payload
      : { t: "MESSAGE_CREATE", d: payload };

    if (eventName !== "MESSAGE_CREATE") {
      return new Response("", { status: 204 });
    }

    if (data.author?.bot) {
      return new Response("", { status: 204 });
    }

    ctx.waitUntil(processMessage(data, env));
    return new Response(null, { status: 204 });
  },
  // Expose reset function for testing
  _resetCaches,
};

async function handleInteraction(
  interaction: DiscordInteraction,
  env: Env
): Promise<Response> {
  const { data, guild_id } = interaction;
  const commandName = data?.name;

  const commandHandlers: Record<
    string,
    (interaction: DiscordInteraction, env: Env) => Promise<Response>
  > = {
    activate: activateCommand,
    status: statusCommand,
  };

  const handler = commandHandlers[commandName || ""];
  if (handler) {
    return handler(interaction, env);
  }

  return jsonResponse({
    type: 4,
    data: {
      flags: 64,
      content: "❓ Unknown command",
    },
  });
}

async function activateCommand(
  interaction: DiscordInteraction,
  env: Env
): Promise<Response> {
  const { guild_id } = interaction;
  const raw = interaction?.data?.options?.[0]?.value ?? "";
  const commuzaApiKey =
    typeof raw === "string" && raw.trim().length > 0 ? raw.trim() : null;

  if (!commuzaApiKey) {
    return jsonResponse({
      type: 4,
      data: {
        flags: 64,
        content:
          "⚠️ Please provide your Commuza application key: `/activate your-commuza-key`\nYou can find this in your Commuza dashboard under API Keys.",
      },
    });
  }

  try {
    const keyValidation = await validateCommuzaApiKey(
      commuzaApiKey,
      env.COMMUZA_API_BASE,
      guild_id
    );

    if (!keyValidation.valid) {
      const errorMessage =
        keyValidation.message ||
        "The provided Commuza API key is invalid. Please check your key and try again.";
      return jsonResponse({
        type: 4,
        data: {
          flags: 64,
          content: `⚠️ ${errorMessage}`,
        },
      });
    }

    const config: GuildConfig = {
      activated: true,
      commuzaApiKey,
      agent_id: keyValidation.agent_id,
      organization_id: keyValidation.organization_id,
    };
    await env.AGENT_CONFIG_KV.put(keyFor(guild_id), JSON.stringify(config));

    return jsonResponse({
      type: 4,
      data: {
        flags: 64,
        content: `✅ Moderation activated with your Commuza application key!\n\nThe agent will now monitor messages in this server.`,
      },
    });
  } catch (error) {
    console.error("Error activating with Commuza key:", error);
    return jsonResponse({
      type: 4,
      data: {
        flags: 64,
        content:
          "❌ An error occurred while verifying your application key. Please try again later or contact support.",
      },
    });
  }
}

async function validateCommuzaApiKey(
  commuzaApiKey: string,
  apiBase: string,
  guildId?: string
): Promise<ApiKeyValidationResult> {
  const now = Date.now();
  console.log(
    `[TEST_DEBUG] validateCommuzaApiKey called for key prefix: ${
      commuzaApiKey
        ? commuzaApiKey.substring(0, Math.min(5, commuzaApiKey.length))
        : "NULL_OR_EMPTY"
    }, validationCache size: ${validationCache.size}`
  );

  // Use a proper cache key that includes the guild ID if provided
  const cacheKey = `${commuzaApiKey}:${guildId || ""}`;

  // Check if we have a cached result for this specific key and guild combination
  const cached = validationCache.get(cacheKey);

  if (cached && cached.expiresAt > now) {
    // If the cached result shows the key was revoked, don't use cache and re-validate
    // This ensures we get the most up-to-date revocation status
    if (cached.revoked) {
      console.log(
        `[TEST_DEBUG] Cached result shows revoked key, re-validating for key prefix: ${commuzaApiKey.substring(
          0,
          Math.min(5, commuzaApiKey.length)
        )}`
      );
    } else {
      console.log(
        `[TEST_DEBUG] Using cached validation for key prefix: ${commuzaApiKey.substring(
          0,
          Math.min(5, commuzaApiKey.length)
        )}`
      );
      return cached;
    }
  }

  console.log(
    `[TEST_DEBUG] Performing API validation (cache miss or expired) for key prefix: ${
      commuzaApiKey
        ? commuzaApiKey.substring(0, Math.min(5, commuzaApiKey.length))
        : "NULL_OR_EMPTY"
    }`
  );

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const payload: any = { service: "discord-agent" };
    if (guildId) {
      payload.platform_data = {
        guild_id: guildId,
      };
    }

    console.log(
      `[TEST_DEBUG] Attempting fetch to: ${apiBase}/api/verify-key with payload:`,
      JSON.stringify(payload)
    );

    // Make the API call to verify the key
    const response = await fetch(`${apiBase}/api/verify-key`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${commuzaApiKey}`,
      },
      body: JSON.stringify(payload),
      signal: controller.signal,
    });

    console.log(
      `[TEST_DEBUG] Fetch call completed for ${apiBase}/api/verify-key, status: ${response.status}`
    );

    clearTimeout(timeoutId);

    let result: ApiKeyValidationResult;
    if (!response.ok) {
      const errorText = await response.text();
      console.error("Key validation error:", response.status, errorText);
      try {
        const errorJson = JSON.parse(errorText);
        result = {
          valid: false,
          message:
            errorJson.message ||
            "Invalid Commuza application key. Please check your key and try again.",
        };
      } catch (e) {
        result = {
          valid: false,
          message:
            "Invalid Commuza application key (unable to parse error details). Please check your key and try again.",
        };
      }
      return result;
    } else {
      const data = (await response.json()) as ApiKeyValidationResult;
      result = {
        valid: !!data.valid,
        message: data.message,
        agent_id: data.agent_id,
        organization_id: data.organization_id,
        key_expires_at: data.key_expires_at,
        days_until_expiry: data.days_until_expiry,
        revoked: data.revoked || false,
      };

      // Check if the key is revoked and mark as invalid
      if (result.revoked) {
        result.valid = false;
        result.message = "API key has been revoked. Please generate a new key.";
        console.log(
          `[TEST_DEBUG] API key is revoked for key prefix: ${commuzaApiKey.substring(
            0,
            Math.min(5, commuzaApiKey.length)
          )}`
        );
      }

      // Add expiration warning to the message if the key is about to expire
      if (
        result.valid &&
        result.days_until_expiry !== undefined &&
        result.days_until_expiry <= 7 &&
        result.days_until_expiry > 0
      ) {
        result.message = `${
          result.message || ""
        } Warning: Your API key will expire in ${result.days_until_expiry} day${
          result.days_until_expiry === 1 ? "" : "s"
        }.`.trim();
      }
    }

    // Cache the result using the proper cache key
    // Use shorter TTL for revoked keys to ensure they get re-validated more frequently
    const cacheTTL = result.revoked ? 60_000 : VALIDATION_CACHE_TTL; // 1 minute for revoked, 5 minutes for others
    validationCache.set(cacheKey, {
      ...result,
      expiresAt: now + cacheTTL,
    });

    return result;
  } catch (error: any) {
    console.error(
      "[TEST_DEBUG] Error during validateCommuzaApiKey fetch process:",
      error
    );

    let message =
      "Could not connect to Commuza API to verify your key. Please try again later.";
    if (error.name === "AbortError") {
      message = "Commuza API key validation timed out. Please try again later.";
    }

    const result = { valid: false, message: message };

    // Cache the error result too to prevent repeated failed calls
    // validationCache.set(cacheKey, {
    //   ...result,
    //   expiresAt: now + VALIDATION_CACHE_TTL,
    // });

    return result;
  }
}

async function statusCommand(
  interaction: DiscordInteraction,
  env: Env
): Promise<Response> {
  const { guild_id } = interaction;
  console.log(
    `[STATUS_COMMAND] Processing status command for guild ${guild_id}`
  );

  const cfgJson = await env.AGENT_CONFIG_KV.get(keyFor(guild_id));
  console.log(
    `[STATUS_COMMAND] Retrieved config from KV: ${
      cfgJson ? "Found" : "Not found"
    }`
  );

  const cfg: GuildConfig | null = cfgJson ? JSON.parse(cfgJson) : null;

  if (!cfg?.activated) {
    return jsonResponse({
      type: 4,
      data: {
        flags: 64,
        content:
          "⚠️ Moderation is **not active** for this server. Use `/activate your-commuza-key` to enable it with a valid Commuza application key.",
      },
    });
  }

  let keyStatusMessage = "✅ Using a valid Commuza application key.";
  let quotaInfo = "";

  if (cfg.commuzaApiKey) {
    try {
      console.log(
        `[STATUS_COMMAND] Validating API key with prefix: ${cfg.commuzaApiKey.substring(
          0,
          Math.min(5, cfg.commuzaApiKey.length)
        )}`
      );

      const validation = await validateCommuzaApiKey(
        cfg.commuzaApiKey,
        env.COMMUZA_API_BASE
      );

      console.log(
        `[STATUS_COMMAND] API key validation result: ${
          validation.valid ? "Valid" : "Invalid"
        }, ${validation.message || "No message"}`
      );
      if (validation.key_expires_at) {
        console.log(
          `[STATUS_COMMAND] API key expires at: ${validation.key_expires_at}, days until expiry: ${validation.days_until_expiry}`
        );
      }

      if (!validation.valid) {
        if (validation.revoked) {
          keyStatusMessage = `⚠️ Your Commuza application key has been revoked. Please generate a new key and re-activate the bot.`;
        } else {
          keyStatusMessage = `⚠️ Your Commuza application key appears to be invalid or expired. Reason: ${
            validation.message || "No specific reason provided."
          }`;
        }
      } else {
        if (validation.message) {
          keyStatusMessage += ` Note: ${validation.message}`;
        }

        // Add expiration information if available
        if (validation.key_expires_at) {
          const expiryDate = new Date(validation.key_expires_at);
          const formattedDate = expiryDate.toLocaleDateString("en-US", {
            year: "numeric",
            month: "long",
            day: "numeric",
          });

          if (validation.days_until_expiry !== undefined) {
            if (validation.days_until_expiry <= 0) {
              quotaInfo += `\n⚠️ **API key has expired** on ${formattedDate}`;
            } else if (validation.days_until_expiry <= 7) {
              quotaInfo += `\n⚠️ **API key expires soon**: ${formattedDate} (${
                validation.days_until_expiry
              } day${validation.days_until_expiry === 1 ? "" : "s"} remaining)`;
            } else {
              quotaInfo += `\n📅 API key expires: ${formattedDate}`;
            }
          } else {
            quotaInfo += `\n📅 API key expires: ${formattedDate}`;
          }
        } else if (!validation.key_expires_at) {
          quotaInfo += `\n📅 API key never expires`;
        }
      }
    } catch (error) {
      console.error("Error re-validating key during status check:", error);
      keyStatusMessage =
        "⚠️ Could not re-verify your Commuza application key. It might be invalid or there was a connection issue.";
    }
  } else {
    keyStatusMessage =
      "⚠️ Moderation is active, but no Commuza application key is currently stored. Please re-activate using `/activate your-commuza-key`.";
  }

  return jsonResponse({
    type: 4,
    data: {
      flags: 64,
      content: `ℹ️ Moderation status: Active.\n${keyStatusMessage}${quotaInfo}\n\nMessages are being scanned for harmful content if the key is valid.`,
    },
  });
}

function jsonResponse(data: any): Response {
  return new Response(JSON.stringify(data), {
    headers: { "Content-Type": "application/json" },
  });
}

async function getGuildConfig(
  guildId: string,
  env: Env
): Promise<GuildConfig | null> {
  const now = Date.now();
  console.log(
    `[TEST_DEBUG] getGuildConfig called for guild: ${guildId}, configCache size: ${configCache.size}`
  );
  const cached = configCache.get(guildId);
  if (cached && cached.expiresAt > now) {
    console.log(`[TEST_DEBUG] Using cached config for guild: ${guildId}`);
    return cached.config;
  }
  console.log(`[TEST_DEBUG] Fetching config from KV for guild: ${guildId}`);
  const cfgJson = await env.AGENT_CONFIG_KV.get(keyFor(guildId));
  const config = cfgJson ? (JSON.parse(cfgJson) as GuildConfig) : null;
  configCache.set(guildId, { config, expiresAt: now + CONFIG_CACHE_TTL });
  console.log(
    `[TEST_DEBUG] Config for guild ${guildId} from KV: ${JSON.stringify(
      config
    )}`
  );
  return config;
}

async function processMessage(msg: DiscordMessage, env: Env): Promise<void> {
  const guildId = msg.guild_id;
  const channelId = msg.channel_id;
  const content = msg.content ?? "";
  console.log(
    `[TEST_DEBUG_PROCESS_MSG] Received message from guild ${guildId}, channel ${channelId}. Content starts with: "${content.substring(
      0,
      20
    )}"`
  );

  try {
    const cfg = await getGuildConfig(guildId, env);
    if (!cfg?.activated) {
      console.log(
        `[TEST_DEBUG_PROCESS_MSG] Guild ${guildId} not activated. Skipping.`
      );
      return;
    }

    if (!cfg.commuzaApiKey) {
      console.warn(
        `[TEST_DEBUG_PROCESS_MSG] No Commuza API key found for guild ${guildId}. Skipping moderation.`
      );
      return;
    }

    console.log(
      `[TEST_DEBUG_PROCESS_MSG] Guild ${guildId} has API key prefix: ${cfg.commuzaApiKey.substring(
        0,
        Math.min(5, cfg.commuzaApiKey.length)
      )}. Calling validateCommuzaApiKey.`
    );

    let agentId = cfg.agent_id;
    let organizationId = cfg.organization_id;

    const keyValidation = await validateCommuzaApiKey(
      cfg.commuzaApiKey,
      env.COMMUZA_API_BASE
    );
    if (!keyValidation.valid) {
      console.warn(
        `[TEST_DEBUG_PROCESS_MSG] Invalid Commuza API key for guild ${guildId} (Reason: ${
          keyValidation.message || "Unknown"
        }). Skipping moderation.`
      );

      // If the key is revoked, we should deactivate the guild configuration
      if (keyValidation.revoked) {
        console.log(
          `[TEST_DEBUG_PROCESS_MSG] API key is revoked for guild ${guildId}. Deactivating guild configuration.`
        );

        // Update the guild configuration to deactivate it
        const updatedConfig = {
          ...cfg,
          activated: false,
          commuzaApiKey: null, // Clear the revoked key
        };

        // Update the KV store
        await env.AGENT_CONFIG_KV.put(
          keyFor(guildId),
          JSON.stringify(updatedConfig)
        );

        // Clear the cache for this guild
        configCache.delete(guildId);
      }

      return;
    }
    console.log("KeyValidation", keyValidation)
    if (keyValidation.agent_id && !agentId) {
      agentId = keyValidation.agent_id;
      organizationId = keyValidation.organization_id;

      const updatedConfig = {
        ...cfg,
        agent_id: agentId,
        organization_id: organizationId,
      };

      env.AGENT_CONFIG_KV.put(
        keyFor(guildId),
        JSON.stringify(updatedConfig)
      ).catch((err) => {
        console.error(
          `[ERROR] Failed to update KV with agent ID: ${err.message}`
        );
      });
    }

    console.log(
      `[TEST_DEBUG_PROCESS_MSG] Commuza API key validated for guild ${guildId}. Proceeding to OpenAI moderation.`
    );

    const mod = await moderate(env.OPENAI_API_KEY, content);
    if (!mod.flagged) {
      if (agentId && env.COMMUZA_DB_URL && env.COMMUZA_DB_KEY) {
        recordMessageAnalytics(
          agentId,
          channelId,
          false,
          env.COMMUZA_DB_URL,
          env.COMMUZA_DB_KEY
        ).catch((err) => {
          console.error(
            `[TEST_DEBUG_ANALYTICS] Error recording message analytics: ${err.message}`
          );
        });
      }
      console.log(
        `[TEST_DEBUG_PROCESS_MSG] Content not flagged by OpenAI for guild ${guildId}.`
      );
      return;
    }
    console.log(
      `[TEST_DEBUG_PROCESS_MSG] Content FLAGGED by OpenAI for guild ${guildId}. Categories: ${JSON.stringify(
        mod.categories
      )}`
    );

    const cats = Object.entries(mod.categories)
      .filter(([_, v]) => v)
      .map(([k]) => k)
      .join(", ");

    const alert = `🚨 **Flagged message** from <@${msg.author.id}> (Categories: ${cats}). Please review.`;

    await postDiscordMessage(channelId, alert, env.DISCORD_BOT_TOKEN);
    console.log(
      `[TEST_DEBUG_PROCESS_MSG] Discord alert posted for guild ${guildId}.`
    );

    if (agentId && env.COMMUZA_DB_URL && env.COMMUZA_DB_KEY) {
      // Get channel name from Discord API if needed in the future
      // For now, we'll use the channel ID as the name
      const channelName = channelId;

      // Get user nickname from Discord API if needed in the future
      // For now, we'll use the user ID as the nickname
      const userNickname = msg.author.id;

      await storeFlaggedMessage(
        agentId,
        msg.id,
        channelId,
        msg.author.id,
        content,
        mod.categories,
        env.COMMUZA_DB_URL,
        env.COMMUZA_DB_KEY,
        guildId,
        channelName,
        userNickname
      );

      await recordMessageAnalytics(
        agentId,
        channelId,
        true,
        env.COMMUZA_DB_URL,
        env.COMMUZA_DB_KEY
      );
    }
  } catch (error) {
    console.error(
      `[TEST_DEBUG_PROCESS_MSG] Error processing message for guild ${guildId}:`,
      error
    );
  }
}

async function recordMessageAnalytics(
  discordAgentId: string,
  channelId: string,
  isFlagged: boolean,
  dbUrl: string,
  dbKey: string
): Promise<void> {
  try {
    // Get today's date in ISO format (YYYY-MM-DD)
    const today = new Date().toISOString().split("T")[0];

    // According to our memory, we should use Supabase insertion for API key operations
    // and avoid using Supabase RPC calls, so we'll go directly to the table insertion

    // Use Supabase upsert functionality for direct table access
    const response = await fetch(`${dbUrl}/rest/v1/message_stats`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        apikey: dbKey,
        Authorization: `Bearer ${dbKey}`,
        Prefer: "resolution=merge-duplicates", // This enables upsert behavior
      },
      body: JSON.stringify({
        agent_id: discordAgentId,
        date: today,
        channel_id: channelId,
        total_messages: 1, // Will be added to existing value on conflict
        flagged_messages: isFlagged ? 1 : 0, // Will be added to existing value on conflict
        updated_at: new Date().toISOString(),
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Failed to record message analytics: ${response.status} - ${errorText}`
      );
    }

    console.log(
      `[TEST_DEBUG_ANALYTICS] Successfully recorded message analytics via direct table access. Flagged: ${isFlagged}`
    );
  } catch (error) {
    console.error("[TEST_DEBUG_ANALYTICS] Error recording analytics:", error);
    throw error;
  }
}

async function storeFlaggedMessage(
  discordAgentId: string,
  messageId: string,
  channelId: string,
  userId: string,
  content: string,
  flagCategories: Record<string, boolean>,
  dbUrl: string,
  dbKey: string,
  guildId: string,
  channelName?: string,
  userNickname?: string
): Promise<void> {
  try {
    // Prepare platform-specific data
    const platformData = {
      guild_id: guildId,
      message_url: `https://discord.com/channels/${guildId}/${channelId}/${messageId}`,
    };

    // Use Supabase insertion with all required fields
    const response = await fetch(`${dbUrl}/rest/v1/flagged_messages`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        apikey: dbKey,
        Authorization: `Bearer ${dbKey}`,
        Prefer: "return=minimal",
      },
      body: JSON.stringify({
        agent_id: discordAgentId,
        message_id: messageId,
        channel_id: channelId,
        channel_name: channelName || channelId, // Use ID as fallback if name not provided
        user_id: userId,
        user_nickname: userNickname || userId, // Use ID as fallback if name not provided
        content: content,
        flagged: true,
        categories: flagCategories,
        platform_data: platformData,
        created_at: new Date().toISOString(),
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Failed to store flagged message: ${response.status} - ${errorText}`
      );
    }

    console.log(
      `[TEST_DEBUG_FLAGGED] Successfully stored flagged message ID: ${messageId}`
    );
  } catch (error) {
    console.error("[TEST_DEBUG_FLAGGED] Error storing flagged message:", error);
    throw error;
  }
}

function keyFor(guildId: string): string {
  return `discord-agent-${guildId}`;
}

async function postDiscordMessage(
  channelId: string,
  content: string,
  botToken: string
): Promise<void> {
  try {
    console.log(
      `[TEST_DEBUG] Attempting to post Discord message to channel ${channelId}`
    );
    const response = await fetch(
      `https://discord.com/api/v10/channels/${channelId}/messages`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bot ${botToken}`,
        },
        body: JSON.stringify({ content }),
      }
    );
    console.log(
      `[TEST_DEBUG] Discord message post response status: ${response.status}`
    );

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Discord API error: ${response.status} - ${errorData}`);
    }
  } catch (error) {
    console.error(
      `Error posting message to Discord channel ${channelId}:`,
      error
    );
  }
}

async function verifyDiscord(
  pubHex: string,
  sigHex: string,
  ts: string,
  body: string
): Promise<boolean> {
  const enc = new TextEncoder();
  const msg = enc.encode(ts + body);
  const sig = hex(sigHex);
  const pub = hex(pubHex);
  try {
    const algorithm = { name: "Ed25519" };
    const key = await crypto.subtle.importKey("raw", pub, algorithm, false, [
      "verify",
    ]);
    return await crypto.subtle.verify(algorithm.name, key, sig, msg);
  } catch (error) {
    console.error("Error during signature verification:", error);
    return false;
  }
}

function hex(h: string): Uint8Array {
  return new Uint8Array(h.match(/.{1,2}/g)?.map((b) => parseInt(b, 16)) || []);
}

async function moderate(
  openaiApiKey: string,
  input: string
): Promise<ModerationResult> {
  if (!input || input.trim() === "") {
    return { flagged: false, categories: {} };
  }
  console.log(
    `[TEST_DEBUG] Calling OpenAI moderation for input starting with: "${input.substring(
      0,
      20
    )}"`
  );

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const res = await fetch("https://api.openai.com/v1/moderations", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${openaiApiKey}`,
      },
      body: JSON.stringify({
        input,
        model: "text-moderation-latest",
      }),
      signal: controller.signal,
    });
    clearTimeout(timeoutId);
    console.log(
      `[TEST_DEBUG] OpenAI moderation response status: ${res.status}`
    );

    if (!res.ok) {
      const errorText = await res.text();
      console.error(`OpenAI API error: ${res.status} - ${errorText}`);
      throw new Error(`OpenAI API error: ${res.status}`);
    }

    const data = (await res.json()) as {
      results?: Array<{
        flagged?: boolean;
        categories?: Record<string, boolean>;
      }>;
    };

    // const data = {
    //   results: [
    //     {
    //       flagged: true,
    //       categories: { "test": true, "not": false}
    //     }
    //   ]
    // }
    const result = data.results?.[0];

    if (!result) {
      console.error(
        "OpenAI Moderation: Unexpected response format, missing results[0]."
      );
      return { flagged: false, categories: {} };
    }
    return {
      flagged: result.flagged ?? false,
      categories: result.categories ?? {},
    };
  } catch (error: any) {
    console.error("[TEST_DEBUG] Error during OpenAI moderate call:", error);
    return { flagged: false, categories: {} };
  }
}
